@import url('../index.css');
@import url('./Procedure.css');
/* ========== HEADER STYLES ========== */
.statistics-placeholder-header {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

.statistics-placeholder-title-row {
  margin-bottom: 20px;
}

.statistics-placeholder-title-with-icon {
  display: flex;
  align-items: center;
  gap: 6px;
}

.statistics-placeholder-title-icon {
  width: 22px;
  height: 22px;
}

.statistics-placeholder-list-title {
  font-size: 22px;
  font-weight: 600;
  color: #5D5D5D;
  margin: 0;
}

.statistics-placeholder-toolbar-row {
  display: flex;
  justify-content: flex-start;
}

.statistics-placeholder-toolbar-left {
  display: flex;
  gap: 12px;
}

/* ========== DROPDOWN STYLES ========== */
.statistics-placeholder-dropdown {
  position: relative;
}

.statistics-placeholder-dropdown-btn {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 140px;
  justify-content: space-between;
}

.statistics-placeholder-dropdown-btn:hover {
  border-color: #007bff;
}

.statistics-placeholder-dropdown-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.statistics-placeholder-dropdown.open .statistics-placeholder-dropdown-icon,
.statistics-placeholder-year-dropdown.open .statistics-placeholder-dropdown-icon {
  transform: rotate(180deg);
}

.statistics-placeholder-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 4px;
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
}

.statistics-placeholder-dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: #5B5B5B;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  transition: background-color 0.2s;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.statistics-placeholder-dropdown-item:last-child {
  border-bottom: none;
}

.statistics-placeholder-dropdown-item:hover {
  background-color: #f8f9fa;
}

.statistics-placeholder-dropdown-item:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.statistics-placeholder-dropdown-item:last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.statistics-placeholder-dropdown-menu::-webkit-scrollbar {
  width: 4px;
}

.statistics-placeholder-dropdown-menu::-webkit-scrollbar-track {
  background: transparent;
}

.statistics-placeholder-dropdown-menu::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

/* ========== STATISTICS CARDS STYLES ========== */
.statistics-placeholder-cards-wrapper {
  margin-bottom: 24px;
}

.statistics-placeholder-cards {
  display: flex;
  gap: 24px;
}

.statistics-placeholder-stat-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 180px;
}

.statistics-placeholder-stat-title {
  font-size: 15px;
  color: #888;
  margin-bottom: 12px;
  font-weight: 500;
}

.statistics-placeholder-stat-title.statistics-placeholder-stat-title-flex {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  gap: 8px;
  flex-direction: row-reverse
}

.statistics-placeholder-stat-icon {
  width: 20px;
  height: 20px;
  opacity: 0.7;
}

.statistics-placeholder-stat-value {
  font-size: 26px;
  font-weight: 700;
  color: #5b5b5b;
  margin-bottom: 8px;
}

.statistics-placeholder-stat-desc {
  font-size: 13px;
  color: #bdbdbd;
}

/* ========== CHART SECTION STYLES ========== */
.statistics-placeholder-chart-section {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  overflow: hidden;
}

.statistics-placeholder-chart-card {
  padding: 24px;
}

.statistics-placeholder-chart-header {
  margin-bottom: 20px;
}

.statistics-placeholder-chart-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.statistics-placeholder-chart-title-icon {
  width: 20px;
  height: 20px;
}

.statistics-placeholder-chart-subtitle {
  font-size: 14px;
  color: #666;
  margin-left: 28px;
}

.statistics-placeholder-chart-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.statistics-placeholder-chart-type-label {
  background: #f0f0f0;
  color: #666;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

/* ========== YEAR FILTER BELOW CHART ========== */
.statistics-placeholder-year-filter {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.statistics-placeholder-year-dropdown {
  position: relative;
}

.statistics-placeholder-year-dropdown-btn {
  background: #fff;
  color: #333;
  border: 1px solid #e0e0e0;
  border-radius: 25px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 140px;
  justify-content: center;
  transition: all 0.2s ease;
}

.statistics-placeholder-year-dropdown-btn:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

.statistics-placeholder-legend-icon {
  width: 20px;
  height: 20px;
}

.statistics-placeholder-year-dropdown-btn .statistics-placeholder-dropdown-icon {
  width: 14px;
  height: 14px;
  transition: transform 0.2s ease;
}

.statistics-placeholder-year-dropdown-menu {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.12);
  z-index: 1000;
  margin-bottom: 8px;
  min-width: 120px;
  overflow: hidden;
}

.statistics-placeholder-year-dropdown-item {
  padding: 12px 20px;
  font-size: 15px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  text-align: center;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
}

.statistics-placeholder-year-dropdown-item:last-child {
  border-bottom: none;
}

.statistics-placeholder-year-dropdown-item:hover {
  background-color: #f8f9fa;
  color: #4F46E5;
}

.statistics-placeholder-chart-container {
  height: 400px;
  width: 100%;
  position: relative;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
}

/* ========== RESPONSIVE STYLES ========== */
@media (max-width: 1200px) {
  .statistics-placeholder-cards {
    flex-wrap: wrap;
  }

  .statistics-placeholder-stat-card {
    min-width: calc(50% - 12px);
  }
}

@media (max-width: 768px) {
  .statistics-placeholder-container {
    padding: 16px;
  }

  .statistics-placeholder-header {
    padding: 16px;
  }

  .statistics-placeholder-chart-card {
    padding: 16px;
  }

  .statistics-placeholder-cards {
    flex-direction: column;
  }

  .statistics-placeholder-stat-card {
    min-width: 100%;
  }

  .statistics-placeholder-chart-container {
    height: 300px;
  }

  .statistics-placeholder-year-dropdown-btn {
    padding: 10px 20px;
    font-size: 14px;
    min-width: 120px;
  }

  .statistics-placeholder-legend-icon {
    width: 18px;
    height: 18px;
  }
}

/* ========== TABLE SECTION STYLES ========== */
.statistics-placeholder-table-section {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  margin-top: 24px;
  overflow: hidden;
}

.statistics-placeholder-table-card {
  padding: 24px;
}

.statistics-placeholder-table-header {
  margin-bottom: 24px;
}

.statistics-placeholder-table-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.statistics-placeholder-table-title-icon {
  width: 20px;
  height: 20px;
}

.statistics-placeholder-table-subtitle {
  font-size: 14px;
  color: #666;
  margin-left: 28px;
}

.statistics-placeholder-table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.statistics-placeholder-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
}

.statistics-placeholder-table .text-left{
  text-align: left;
}

.statistics-placeholder-table th {
  background: #f8f9fa;
  padding: 16px 12px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
  white-space: nowrap;
}

.statistics-placeholder-table td {
  padding: 16px 12px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  color: #333;
  vertical-align: middle;
}

.statistics-placeholder-table tbody tr:hover {
  background-color: #f8f9fa;
}

.statistics-placeholder-table .text-center {
  text-align: center;
}

.employee-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.employee-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.employee-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.employee-name {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.employee-position {
  font-size: 12px;
  color: #666;
}

.statistics-placeholder-status-chart {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: center;
}

.statistics-placeholder-status-legend {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px 8px;
  align-items: center;
}

.statistics-placeholder-legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.statistics-placeholder-legend-color {
  width: 8px;
  height: 8px;
  border-radius: 2px;
  flex-shrink: 0;
}

.statistics-placeholder-legend-label {
  font-size: 10px;
  color: #666;
  font-weight: 500;
  min-width: 12px;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background-color: #f0f0f0;
}

.action-icon {
  width: 16px;
  height: 16px;
}

/* Responsive table styles */
@media (max-width: 768px) {
  .statistics-placeholder-table-card {
    padding: 16px;
  }

  .statistics-placeholder-table-container {
    font-size: 12px;
  }

  .statistics-placeholder-table th,
  .statistics-placeholder-table td {
    padding: 12px 8px;
  }

  .employee-avatar {
    width: 28px;
    height: 28px;
  }

  .employee-name {
    font-size: 13px;
  }

  .employee-position {
    font-size: 11px;
  }
}
